const { contextBridge, ipc<PERSON>enderer } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // App info
  getAppVersion: () => ipcRenderer.invoke('get-app-version'),
  
  // Dialog methods
  showMessageBox: (options) => ipcRenderer.invoke('show-message-box', options),
  showSaveDialog: (options) => ipcRenderer.invoke('show-save-dialog', options),
  showOpenDialog: (options) => ipcRenderer.invoke('show-open-dialog', options),
  
  // Menu actions
  onMenuAction: (callback) => ipcRenderer.on('menu-action', callback),
  removeMenuActionListener: (callback) => ipcRenderer.removeListener('menu-action', callback),
  
  // Server control
  startServer: () => ipcRenderer.invoke('start-server'),
  stopServer: () => ipc<PERSON>enderer.invoke('stop-server'),
  restartServer: () => ipcRenderer.invoke('restart-server'),
  getServerStatus: () => ipcRenderer.invoke('get-server-status'),
  
  // Player management
  getPlayers: () => ipcRenderer.invoke('get-players'),
  kickPlayer: (playerName, reason) => ipcRenderer.invoke('kick-player', playerName, reason),
  banPlayer: (playerName, reason) => ipcRenderer.invoke('ban-player', playerName, reason),
  unbanPlayer: (playerName) => ipcRenderer.invoke('unban-player', playerName),
  
  // Server logs
  getLogs: (lines) => ipcRenderer.invoke('get-logs', lines),
  onLogUpdate: (callback) => ipcRenderer.on('log-update', callback),
  removeLogUpdateListener: (callback) => ipcRenderer.removeListener('log-update', callback),
  
  // Server stats
  getServerStats: () => ipcRenderer.invoke('get-server-stats'),
  onStatsUpdate: (callback) => ipcRenderer.on('stats-update', callback),
  removeStatsUpdateListener: (callback) => ipcRenderer.removeListener('stats-update', callback),
  
  // Database operations
  executeQuery: (query, params) => ipcRenderer.invoke('execute-query', query, params),
  getPlayerData: (playerName) => ipcRenderer.invoke('get-player-data', playerName),
  
  // Backup operations
  createBackup: (name, description) => ipcRenderer.invoke('create-backup', name, description),
  restoreBackup: (backupId) => ipcRenderer.invoke('restore-backup', backupId),
  getBackups: () => ipcRenderer.invoke('get-backups'),
  deleteBackup: (backupId) => ipcRenderer.invoke('delete-backup', backupId),
  
  // Configuration
  getServerProperties: () => ipcRenderer.invoke('get-server-properties'),
  updateServerProperties: (properties) => ipcRenderer.invoke('update-server-properties', properties),
  getYamlConfig: (configPath) => ipcRenderer.invoke('get-yaml-config', configPath),
  updateYamlConfig: (configPath, configData) => ipcRenderer.invoke('update-yaml-config', configPath, configData),
  validateConfiguration: (configPath, configData) => ipcRenderer.invoke('validate-configuration', configPath, configData),
  createConfigBackup: (configFileName) => ipcRenderer.invoke('create-config-backup', configFileName),
  getConfigBackups: (configFileName) => ipcRenderer.invoke('get-config-backups', configFileName),
  restoreConfigBackup: (backupPath, configFileName) => ipcRenderer.invoke('restore-config-backup', backupPath, configFileName),
  getPluginConfig: (pluginName) => ipcRenderer.invoke('get-plugin-config', pluginName),
  updatePluginConfig: (pluginName, config) => ipcRenderer.invoke('update-plugin-config', pluginName, config),
  
  // Plugin management
  getInstalledPlugins: () => ipcRenderer.invoke('get-installed-plugins'),
  enablePlugin: (pluginId) => ipcRenderer.invoke('enable-plugin', pluginId),
  disablePlugin: (pluginId) => ipcRenderer.invoke('disable-plugin', pluginId),
  deletePlugin: (pluginId) => ipcRenderer.invoke('delete-plugin', pluginId),
  getPluginConfigContent: (pluginName) => ipcRenderer.invoke('get-plugin-config-content', pluginName),
  updatePluginConfigContent: (pluginName, configContent) => ipcRenderer.invoke('update-plugin-config-content', pluginName, configContent),
  reloadPlugins: () => ipcRenderer.invoke('reload-plugins'),
  
  // File operations
  readFile: (filePath) => ipcRenderer.invoke('read-file', filePath),
  writeFile: (filePath, content) => ipcRenderer.invoke('write-file', filePath, content),
  
  // Authentication
  login: (username, password) => ipcRenderer.invoke('login', username, password),
  logout: (sessionId) => ipcRenderer.invoke('logout', sessionId),
  validateSession: (sessionId) => ipcRenderer.invoke('validate-session', sessionId),
  getCurrentUser: (sessionId) => ipcRenderer.invoke('get-current-user', sessionId),

  // User Management
  getUsers: () => ipcRenderer.invoke('get-users'),
  createUser: (userData) => ipcRenderer.invoke('create-user', userData),
  updateUser: (userData) => ipcRenderer.invoke('update-user', userData),
  deleteUser: (userId) => ipcRenderer.invoke('delete-user', userId),

  // Security Configuration
  getSecurityConfig: () => ipcRenderer.invoke('get-security-config'),
  updateSecurityConfig: (config) => ipcRenderer.invoke('update-security-config', config),
  getSecurityLogs: (limit) => ipcRenderer.invoke('get-security-logs', limit),
  getActiveSessions: () => ipcRenderer.invoke('get-active-sessions'),
  invalidateSessions: (userId) => ipcRenderer.invoke('invalidate-sessions', userId),
  
  // Notifications
  onNotification: (callback) => ipcRenderer.on('notification', callback),
  removeNotificationListener: (callback) => ipcRenderer.removeListener('notification', callback),
  onNotificationUpdate: (callback) => ipcRenderer.on('notification-update', callback),
  removeNotificationUpdateListener: (callback) => ipcRenderer.removeListener('notification-update', callback),

  // Notification Management
  getNotifications: (filters) => ipcRenderer.invoke('get-notifications', filters),
  createNotification: (type, title, message, data) => ipcRenderer.invoke('create-notification', type, title, message, data),
  markNotificationRead: (notificationId) => ipcRenderer.invoke('mark-notification-read', notificationId),
  markAllNotificationsRead: (category) => ipcRenderer.invoke('mark-all-notifications-read', category),
  dismissNotification: (notificationId) => ipcRenderer.invoke('dismiss-notification', notificationId),
  deleteNotification: (notificationId) => ipcRenderer.invoke('delete-notification', notificationId),
  clearAllNotifications: (category) => ipcRenderer.invoke('clear-all-notifications', category),
  getNotificationSettings: () => ipcRenderer.invoke('get-notification-settings'),
  updateNotificationSettings: (settings) => ipcRenderer.invoke('update-notification-settings', settings),
  getNotificationStatistics: () => ipcRenderer.invoke('get-notification-statistics'),

  // Development/Testing methods
  setManualServerStatus: (status) => ipcRenderer.invoke('set-manual-server-status', status),
  clearManualServerStatus: () => ipcRenderer.invoke('clear-manual-server-status')
});

// Expose a limited set of Node.js APIs
contextBridge.exposeInMainWorld('nodeAPI', {
  platform: process.platform,
  arch: process.arch,
  versions: process.versions
});
