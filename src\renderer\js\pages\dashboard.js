// Dashboard Page
class DashboardPage {
    static async render(container) {
        try {
            const html = `
                <div class="dashboard-grid">
                    <div class="stat-card">
                        <div class="stat-card-header">
                            <div class="stat-card-title">Server Status</div>
                            <div class="stat-card-icon primary">
                                <i class="fas fa-server"></i>
                            </div>
                        </div>
                        <div class="stat-card-value" id="server-status-value">Offline</div>
                        <div class="stat-card-change neutral" id="server-uptime">
                            <i class="fas fa-clock"></i>
                            <span>Uptime: 0h 0m</span>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-card-header">
                            <div class="stat-card-title">Online Players</div>
                            <div class="stat-card-icon success">
                                <i class="fas fa-users"></i>
                            </div>
                        </div>
                        <div class="stat-card-value" id="online-players">0</div>
                        <div class="stat-card-change neutral" id="max-players">
                            <i class="fas fa-user-plus"></i>
                            <span>Max: 100</span>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-card-header">
                            <div class="stat-card-title">Memory Usage</div>
                            <div class="stat-card-icon warning">
                                <i class="fas fa-memory"></i>
                            </div>
                        </div>
                        <div class="stat-card-value" id="memory-usage">0%</div>
                        <div class="stat-card-change neutral" id="memory-details">
                            <i class="fas fa-info-circle"></i>
                            <span>0 GB / 15 GB</span>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-card-header">
                            <div class="stat-card-title">CPU Usage</div>
                            <div class="stat-card-icon error">
                                <i class="fas fa-microchip"></i>
                            </div>
                        </div>
                        <div class="stat-card-value" id="cpu-usage">0%</div>
                        <div class="stat-card-change neutral" id="cpu-details">
                            <i class="fas fa-chart-line"></i>
                            <span>Load Average</span>
                        </div>
                    </div>
                </div>

                <div class="dashboard-row">
                    <div class="chart-container">
                        <div class="chart-header">
                            <h3 class="chart-title">Server Performance</h3>
                            <div class="chart-controls">
                                <button class="chart-control active" data-period="1h">1H</button>
                                <button class="chart-control" data-period="6h">6H</button>
                                <button class="chart-control" data-period="24h">24H</button>
                                <button class="chart-control" data-period="7d">7D</button>
                            </div>
                        </div>
                        <div class="chart-wrapper">
                            <canvas id="performance-chart"></canvas>
                        </div>
                    </div>

                    <div class="activity-feed">
                        <div class="activity-header">
                            <h3 class="activity-title">Recent Activity</h3>
                        </div>
                        <div class="activity-list" id="activity-list">
                            <!-- Activity items will be loaded here -->
                        </div>
                    </div>
                </div>

                <div class="dashboard-row">
                    <div class="server-status-panel">
                        <div class="server-status-header">
                            <h3 class="server-status-title">Server Control</h3>
                            <div class="server-status-badge offline" id="status-badge">
                                <div class="status-indicator offline"></div>
                                <span>Offline</span>
                            </div>
                        </div>
                        
                        <div class="server-info-grid">
                            <div class="server-info-item">
                                <div class="server-info-label">Version</div>
                                <div class="server-info-value" id="server-version">Paper 1.20.4</div>
                            </div>
                            <div class="server-info-item">
                                <div class="server-info-label">Port</div>
                                <div class="server-info-value" id="server-port">25565</div>
                            </div>
                            <div class="server-info-item">
                                <div class="server-info-label">World</div>
                                <div class="server-info-value" id="server-world">world</div>
                            </div>
                            <div class="server-info-item">
                                <div class="server-info-label">Difficulty</div>
                                <div class="server-info-value" id="server-difficulty">Hard</div>
                            </div>
                        </div>

                        <div class="server-actions">
                            <button class="btn btn-success" id="start-server-btn" onclick="DashboardPage.startServer()">
                                <i class="fas fa-play"></i>
                                Start Server
                            </button>
                            <button class="btn btn-warning" id="restart-server-btn" onclick="DashboardPage.restartServer()" disabled>
                                <i class="fas fa-redo"></i>
                                Restart
                            </button>
                            <button class="btn btn-error" id="stop-server-btn" onclick="DashboardPage.stopServer()" disabled>
                                <i class="fas fa-stop"></i>
                                Stop Server
                            </button>
                        </div>
                    </div>

                    <div class="quick-actions">
                        <h3 class="quick-actions-title">Quick Actions</h3>
                        <div class="quick-actions-grid">
                            <a href="#" class="quick-action" onclick="DashboardPage.quickAction('backup')">
                                <div class="quick-action-icon">
                                    <i class="fas fa-save"></i>
                                </div>
                                <div class="quick-action-label">Create Backup</div>
                            </a>
                            <a href="#" class="quick-action" onclick="DashboardPage.quickAction('logs')">
                                <div class="quick-action-icon">
                                    <i class="fas fa-file-alt"></i>
                                </div>
                                <div class="quick-action-label">View Logs</div>
                            </a>
                            <a href="#" class="quick-action" onclick="DashboardPage.quickAction('players')">
                                <div class="quick-action-icon">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="quick-action-label">Manage Players</div>
                            </a>
                            <a href="#" class="quick-action" onclick="DashboardPage.quickAction('plugins')">
                                <div class="quick-action-icon">
                                    <i class="fas fa-puzzle-piece"></i>
                                </div>
                                <div class="quick-action-label">Plugins</div>
                            </a>
                            <a href="#" class="quick-action" onclick="DashboardPage.quickAction('settings')">
                                <div class="quick-action-icon">
                                    <i class="fas fa-cog"></i>
                                </div>
                                <div class="quick-action-label">Settings</div>
                            </a>
                            <a href="#" class="quick-action" onclick="DashboardPage.quickAction('database')">
                                <div class="quick-action-icon">
                                    <i class="fas fa-database"></i>
                                </div>
                                <div class="quick-action-label">Database</div>
                            </a>
                        </div>
                    </div>
                </div>
            `;

            container.innerHTML = html;
            
            // Initialize components
            await this.initializeComponents();
            
        } catch (error) {
            console.error('Error rendering dashboard:', error);
            container.innerHTML = '<div class="alert alert-error">Failed to load dashboard</div>';
        }
    }

    static async initializeComponents() {
        // Initialize performance chart
        this.initPerformanceChart();
        
        // Load initial data
        await this.loadDashboardData();
        
        // Set up periodic updates
        this.startPeriodicUpdates();
        
        // Bind chart controls
        this.bindChartControls();
    }

    static initPerformanceChart() {
        const ctx = document.getElementById('performance-chart');
        if (!ctx) return;

        this.performanceChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [
                    {
                        label: 'CPU Usage (%)',
                        data: [],
                        borderColor: chartColors.primary,
                        backgroundColor: chartColors.primary + '20',
                        tension: 0.4,
                        fill: true
                    },
                    {
                        label: 'Memory Usage (%)',
                        data: [],
                        borderColor: chartColors.warning,
                        backgroundColor: chartColors.warning + '20',
                        tension: 0.4,
                        fill: true
                    },
                    {
                        label: 'Players Online',
                        data: [],
                        borderColor: chartColors.success,
                        backgroundColor: chartColors.success + '20',
                        tension: 0.4,
                        fill: true,
                        yAxisID: 'y1'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        title: {
                            display: true,
                            text: 'Usage (%)'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Players'
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                }
            }
        });
    }

    static bindChartControls() {
        const controls = document.querySelectorAll('.chart-control');
        controls.forEach(control => {
            control.addEventListener('click', (e) => {
                // Update active state
                controls.forEach(c => c.classList.remove('active'));
                e.target.classList.add('active');
                
                // Update chart data
                const period = e.target.dataset.period;
                this.updateChartData(period);
            });
        });
    }

    static async loadDashboardData() {
        try {
            // Load server status
            await this.updateServerStatus();
            
            // Load activity feed
            await this.loadActivityFeed();
            
            // Load performance data
            await this.updateChartData('1h');
            
        } catch (error) {
            console.error('Error loading dashboard data:', error);
        }
    }

    static async updateServerStatus() {
        try {
            // Get real server status from API
            let status = {
                online: false,
                players: 0,
                maxPlayers: 100,
                memoryUsage: 0,
                memoryTotal: 15360, // 15GB in MB
                cpuUsage: 0,
                uptime: 0,
                version: 'Paper 1.20.4',
                port: 25565,
                world: 'world',
                difficulty: 'Hard'
            };

            if (window.electronAPI) {
                try {
                    const serverData = await window.electronAPI.getServerStatus();
                    if (serverData) {
                        status = {
                            online: serverData.status === 'online',
                            players: serverData.players || 0,
                            maxPlayers: serverData.maxPlayers || 100,
                            memoryUsage: serverData.memory?.used ? Math.round(serverData.memory.used / (1024 * 1024)) : 0,
                            memoryTotal: serverData.memory?.total ? Math.round(serverData.memory.total / (1024 * 1024)) : 15360,
                            cpuUsage: serverData.cpu || 0,
                            uptime: serverData.uptime || 0,
                            version: serverData.version || 'Minecraft Server',
                            port: serverData.port || 25565,
                            world: 'world',
                            difficulty: serverData.difficulty || 'Hard'
                        };
                    }
                } catch (error) {
                    console.error('Error fetching server status:', error);
                    // Use default mock data if API fails
                }
            }

            // Update UI elements
            document.getElementById('server-status-value').textContent = status.online ? 'Online' : 'Offline';
            document.getElementById('online-players').textContent = status.players;
            document.getElementById('memory-usage').textContent = Math.round((status.memoryUsage / status.memoryTotal) * 100) + '%';
            document.getElementById('memory-details').innerHTML = `<i class="fas fa-info-circle"></i><span>${formatBytes(status.memoryUsage * 1024 * 1024)} / ${formatBytes(status.memoryTotal * 1024 * 1024)}</span>`;
            document.getElementById('cpu-usage').textContent = status.cpuUsage + '%';
            document.getElementById('server-uptime').innerHTML = `<i class="fas fa-clock"></i><span>Uptime: ${formatDuration(status.uptime)}</span>`;
            
            // Update server info
            document.getElementById('server-version').textContent = status.version;
            document.getElementById('server-port').textContent = status.port;
            document.getElementById('server-world').textContent = status.world;
            document.getElementById('server-difficulty').textContent = status.difficulty;
            
            // Update status badge
            const statusBadge = document.getElementById('status-badge');
            const statusClass = status.online ? 'online' : 'offline';
            statusBadge.className = `server-status-badge ${statusClass}`;
            statusBadge.innerHTML = `
                <div class="status-indicator ${statusClass}"></div>
                <span>${status.online ? 'Online' : 'Offline'}</span>
            `;
            
            // Update action buttons
            const startBtn = document.getElementById('start-server-btn');
            const restartBtn = document.getElementById('restart-server-btn');
            const stopBtn = document.getElementById('stop-server-btn');
            
            if (status.online) {
                startBtn.disabled = true;
                restartBtn.disabled = false;
                stopBtn.disabled = false;
            } else {
                startBtn.disabled = false;
                restartBtn.disabled = true;
                stopBtn.disabled = true;
            }
            
        } catch (error) {
            console.error('Error updating server status:', error);
        }
    }

    static async loadActivityFeed() {
        const activityList = document.getElementById('activity-list');
        if (!activityList) return;

        // Mock activity data
        const activities = [
            {
                type: 'info',
                message: 'Server started successfully',
                time: new Date(Date.now() - 5 * 60 * 1000)
            },
            {
                type: 'success',
                message: 'Player Steve joined the game',
                time: new Date(Date.now() - 15 * 60 * 1000)
            },
            {
                type: 'warning',
                message: 'High memory usage detected (85%)',
                time: new Date(Date.now() - 30 * 60 * 1000)
            },
            {
                type: 'error',
                message: 'Plugin WorldEdit failed to load',
                time: new Date(Date.now() - 45 * 60 * 1000)
            },
            {
                type: 'info',
                message: 'Backup completed successfully',
                time: new Date(Date.now() - 60 * 60 * 1000)
            }
        ];

        const iconMap = {
            info: 'fas fa-info-circle',
            success: 'fas fa-check-circle',
            warning: 'fas fa-exclamation-triangle',
            error: 'fas fa-times-circle'
        };

        activityList.innerHTML = activities.map(activity => `
            <div class="activity-item">
                <div class="activity-icon ${activity.type}">
                    <i class="${iconMap[activity.type]}"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-message">${escapeHtml(activity.message)}</div>
                    <div class="activity-time">${formatRelativeTime(activity.time)}</div>
                </div>
            </div>
        `).join('');
    }

    static async updateChartData(period) {
        if (!this.performanceChart) return;

        // Generate mock data based on period
        const now = new Date();
        const labels = [];
        const cpuData = [];
        const memoryData = [];
        const playersData = [];

        let points, interval;
        switch (period) {
            case '1h':
                points = 12;
                interval = 5 * 60 * 1000; // 5 minutes
                break;
            case '6h':
                points = 24;
                interval = 15 * 60 * 1000; // 15 minutes
                break;
            case '24h':
                points = 24;
                interval = 60 * 60 * 1000; // 1 hour
                break;
            case '7d':
                points = 28;
                interval = 6 * 60 * 60 * 1000; // 6 hours
                break;
            default:
                points = 12;
                interval = 5 * 60 * 1000;
        }

        for (let i = points - 1; i >= 0; i--) {
            const time = new Date(now.getTime() - i * interval);
            labels.push(time.toLocaleTimeString('en-US', { 
                hour: '2-digit', 
                minute: '2-digit',
                hour12: false 
            }));
            
            // Generate realistic mock data
            cpuData.push(Math.random() * 60 + 20); // 20-80%
            memoryData.push(Math.random() * 40 + 40); // 40-80%
            playersData.push(Math.floor(Math.random() * 20)); // 0-20 players
        }

        this.performanceChart.data.labels = labels;
        this.performanceChart.data.datasets[0].data = cpuData;
        this.performanceChart.data.datasets[1].data = memoryData;
        this.performanceChart.data.datasets[2].data = playersData;
        this.performanceChart.update();
    }

    static startPeriodicUpdates() {
        // Update every 30 seconds
        this.updateInterval = setInterval(() => {
            this.updateServerStatus();
        }, 30000);
    }

    static async startServer() {
        try {
            toastNotifications.show('Server', 'Starting server...', 'info');
            if (window.electronAPI) {
                await window.electronAPI.startServer();
            }
        } catch (error) {
            console.error('Error starting server:', error);
            toastNotifications.show('Error', 'Failed to start server', 'error');
        }
    }

    static async stopServer() {
        showConfirmDialog(
            'Stop Server',
            'Are you sure you want to stop the server? All players will be disconnected.',
            async () => {
                try {
                    toastNotifications.show('Server', 'Stopping server...', 'info');
                    if (window.electronAPI) {
                        await window.electronAPI.stopServer();
                    }
                } catch (error) {
                    console.error('Error stopping server:', error);
                    toastNotifications.show('Error', 'Failed to stop server', 'error');
                }
            }
        );
    }

    static async restartServer() {
        showConfirmDialog(
            'Restart Server',
            'Are you sure you want to restart the server? All players will be temporarily disconnected.',
            async () => {
                try {
                    toastNotifications.show('Server', 'Restarting server...', 'info');
                    if (window.electronAPI) {
                        await window.electronAPI.restartServer();
                    }
                } catch (error) {
                    console.error('Error restarting server:', error);
                    toastNotifications.show('Error', 'Failed to restart server', 'error');
                }
            }
        );
    }

    static quickAction(action) {
        switch (action) {
            case 'backup':
                window.app?.loadPage('backups');
                break;
            case 'logs':
                window.app?.loadPage('logs');
                break;
            case 'players':
                window.app?.loadPage('players');
                break;
            case 'plugins':
                window.app?.loadPage('plugins');
                break;
            case 'settings':
                window.app?.loadPage('settings');
                break;
            case 'database':
                window.app?.loadPage('database');
                break;
        }
    }

    static cleanup() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }
        if (this.performanceChart) {
            this.performanceChart.destroy();
        }
    }
}
