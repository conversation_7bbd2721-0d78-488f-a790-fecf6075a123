/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Color Palette */
    --primary-color: #3b82f6;
    --primary-dark: #2563eb;
    --primary-light: #60a5fa;
    --secondary-color: #6366f1;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --info-color: #06b6d4;
    
    /* Neutral Colors */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    
    /* Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-dark: #0f172a;
    --bg-sidebar: #1e293b;
    --bg-header: #ffffff;
    
    /* Text Colors */
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-tertiary: #9ca3af;
    --text-inverse: #ffffff;
    
    /* Border Colors */
    --border-color: #e5e7eb;
    --border-light: #f3f4f6;
    --border-dark: #374151;
    
    /* Shadow */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    
    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;
    
    /* Z-index */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    --z-toast: 1080;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    line-height: 1.5;
    color: var(--text-primary);
    background-color: var(--bg-secondary);
    overflow: hidden;
}

/* App Layout */
.app-container {
    display: flex;
    height: 100vh;
    width: 100vw;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.95);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: var(--z-modal);
    transition: opacity var(--transition-normal);
}

.loading-overlay.hidden {
    opacity: 0;
    pointer-events: none;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--gray-200);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: var(--spacing-md);
}

.loading-text {
    color: var(--text-secondary);
    font-weight: 500;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Sidebar */
.sidebar {
    width: 280px;
    background: var(--bg-sidebar);
    color: var(--text-inverse);
    display: flex;
    flex-direction: column;
    transition: width var(--transition-normal);
    z-index: var(--z-fixed);
}

.sidebar.collapsed {
    width: 70px;
}

.sidebar-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: 600;
    font-size: 18px;
}

.logo i {
    font-size: 24px;
    color: var(--primary-light);
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--text-inverse);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: background-color var(--transition-fast);
}

.sidebar-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
}

.sidebar-menu {
    flex: 1;
    padding: var(--spacing-lg) 0;
    overflow-y: auto;
}

.menu-section {
    margin-bottom: var(--spacing-xl);
}

.menu-title {
    padding: 0 var(--spacing-lg);
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    color: var(--gray-400);
    margin-bottom: var(--spacing-sm);
}

.menu-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-lg);
    color: var(--gray-300);
    text-decoration: none;
    transition: all var(--transition-fast);
    position: relative;
}

.menu-item:hover {
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-inverse);
}

.menu-item.active {
    background: rgba(59, 130, 246, 0.1);
    color: var(--primary-light);
}

.menu-item.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: var(--primary-color);
}

.menu-item i {
    width: 20px;
    text-align: center;
}

.sidebar-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.user-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.user-details {
    flex: 1;
}

.user-name {
    font-weight: 600;
    font-size: 14px;
}

.user-role {
    font-size: 12px;
    color: var(--gray-400);
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Header */
.header {
    background: var(--bg-header);
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-lg) var(--spacing-xl);
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: var(--shadow-sm);
}

.header-left {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.page-title {
    font-size: 24px;
    font-weight: 600;
    color: var(--text-primary);
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 12px;
    color: var(--text-secondary);
}

.breadcrumb i {
    font-size: 10px;
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.server-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--bg-tertiary);
    border-radius: var(--radius-lg);
    font-size: 13px;
    font-weight: 500;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--error-color);
}

.status-indicator.online {
    background: var(--success-color);
}

.status-indicator.starting {
    background: var(--warning-color);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

/* Content Area */
.content-area {
    flex: 1;
    padding: var(--spacing-xl);
    overflow-y: auto;
    background: var(--bg-secondary);
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        left: -280px;
        height: 100vh;
        z-index: var(--z-modal);
        transition: left var(--transition-normal);
    }

    .sidebar.open {
        left: 0;
    }

    .main-content {
        width: 100%;
    }

    .header {
        padding: var(--spacing-md) var(--spacing-lg);
    }

    .content-area {
        padding: var(--spacing-lg);
    }

    .page-title {
        font-size: 20px;
    }
}

/* Utility Classes */
.hidden {
    display: none !important;
}

/* Override for loading overlay - use opacity instead of display:none for smooth transition */
.loading-overlay.hidden {
    opacity: 0 !important;
    pointer-events: none !important;
    visibility: hidden !important;
    display: flex !important; /* Keep flex to maintain transition */
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.text-left {
    text-align: left;
}

.font-bold {
    font-weight: 700;
}

.font-semibold {
    font-weight: 600;
}

.font-medium {
    font-weight: 500;
}

.text-sm {
    font-size: 12px;
}

.text-lg {
    font-size: 16px;
}

.text-xl {
    font-size: 18px;
}

.text-2xl {
    font-size: 24px;
}

.text-primary {
    color: var(--primary-color);
}

.text-success {
    color: var(--success-color);
}

.text-warning {
    color: var(--warning-color);
}

.text-error {
    color: var(--error-color);
}

.text-secondary {
    color: var(--text-secondary);
}

.bg-primary {
    background-color: var(--primary-color);
}

.bg-success {
    background-color: var(--success-color);
}

.bg-warning {
    background-color: var(--warning-color);
}

.bg-error {
    background-color: var(--error-color);
}

.rounded {
    border-radius: var(--radius-md);
}

.rounded-lg {
    border-radius: var(--radius-lg);
}

.shadow {
    box-shadow: var(--shadow-md);
}

.shadow-lg {
    box-shadow: var(--shadow-lg);
}

.mb-2 {
    margin-bottom: var(--spacing-sm);
}

.mb-4 {
    margin-bottom: var(--spacing-md);
}

.mb-6 {
    margin-bottom: var(--spacing-lg);
}

.mt-2 {
    margin-top: var(--spacing-sm);
}

.mt-4 {
    margin-top: var(--spacing-md);
}

.mt-6 {
    margin-top: var(--spacing-lg);
}

.p-2 {
    padding: var(--spacing-sm);
}

.p-4 {
    padding: var(--spacing-md);
}

.p-6 {
    padding: var(--spacing-lg);
}

.flex {
    display: flex;
}

.flex-col {
    flex-direction: column;
}

.items-center {
    align-items: center;
}

.justify-center {
    justify-content: center;
}

.justify-between {
    justify-content: space-between;
}

.gap-2 {
    gap: var(--spacing-sm);
}

.gap-4 {
    gap: var(--spacing-md);
}

.w-full {
    width: 100%;
}

.h-full {
    height: 100%;
}
