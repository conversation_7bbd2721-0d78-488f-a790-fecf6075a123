const fs = require('fs').promises;
const path = require('path');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');

class SecurityService {
    constructor() {
        this.usersPath = path.join(process.cwd(), 'data', 'users.json');
        this.sessionsPath = path.join(process.cwd(), 'data', 'sessions.json');
        this.securityConfigPath = path.join(process.cwd(), 'data', 'security-config.json');
        this.jwtSecret = process.env.JWT_SECRET || this.generateSecret();
        this.activeSessions = new Map();
        
        this.initializeService();
    }

    async initializeService() {
        try {
            await fs.mkdir(path.dirname(this.usersPath), { recursive: true });
            
            // Initialize users file if it doesn't exist
            try {
                await fs.access(this.usersPath);
            } catch (error) {
                const defaultUsers = {
                    users: [
                        {
                            id: 'admin',
                            username: 'admin',
                            email: 'admin@localhost',
                            passwordHash: await bcrypt.hash('admin', 10),
                            role: 'administrator',
                            permissions: ['*'],
                            created: new Date().toISOString(),
                            lastLogin: null,
                            active: true
                        }
                    ],
                    lastUpdated: new Date().toISOString()
                };
                await fs.writeFile(this.usersPath, JSON.stringify(defaultUsers, null, 2));
            }

            // Initialize security config
            try {
                await fs.access(this.securityConfigPath);
            } catch (error) {
                const defaultConfig = {
                    sessionTimeout: 24 * 60 * 60 * 1000, // 24 hours
                    maxLoginAttempts: 5,
                    lockoutDuration: 15 * 60 * 1000, // 15 minutes
                    passwordPolicy: {
                        minLength: 8,
                        requireUppercase: true,
                        requireLowercase: true,
                        requireNumbers: true,
                        requireSpecialChars: false
                    },
                    twoFactorEnabled: false,
                    auditLogging: true
                };
                await fs.writeFile(this.securityConfigPath, JSON.stringify(defaultConfig, null, 2));
            }

            console.log('Security service initialized');
        } catch (error) {
            console.error('Failed to initialize security service:', error);
        }
    }

    generateSecret() {
        return crypto.randomBytes(64).toString('hex');
    }

    // Authentication Methods
    async authenticate(username, password) {
        try {
            const users = await this.getUsers();
            const user = users.find(u => u.username === username && u.active);
            
            if (!user) {
                await this.logSecurityEvent('login_failed', { username, reason: 'user_not_found' });
                throw new Error('Invalid credentials');
            }

            // Check if user is locked out
            if (await this.isUserLockedOut(username)) {
                await this.logSecurityEvent('login_failed', { username, reason: 'account_locked' });
                throw new Error('Account is temporarily locked due to too many failed attempts');
            }

            const isValidPassword = await bcrypt.compare(password, user.passwordHash);
            
            if (!isValidPassword) {
                await this.recordFailedLogin(username);
                await this.logSecurityEvent('login_failed', { username, reason: 'invalid_password' });
                throw new Error('Invalid credentials');
            }

            // Clear failed login attempts
            await this.clearFailedLogins(username);

            // Update last login
            user.lastLogin = new Date().toISOString();
            await this.updateUser(user);

            // Generate JWT token
            const token = jwt.sign(
                { 
                    userId: user.id,
                    username: user.username,
                    role: user.role,
                    permissions: user.permissions
                },
                this.jwtSecret,
                { expiresIn: '24h' }
            );

            // Create session
            const session = {
                id: crypto.randomUUID(),
                userId: user.id,
                username: user.username,
                token: token,
                created: new Date().toISOString(),
                lastActivity: new Date().toISOString(),
                ipAddress: null, // Would be set by the caller
                userAgent: null  // Would be set by the caller
            };

            this.activeSessions.set(session.id, session);
            await this.saveSessions();

            await this.logSecurityEvent('login_success', { username });

            return {
                success: true,
                user: {
                    id: user.id,
                    username: user.username,
                    email: user.email,
                    role: user.role,
                    permissions: user.permissions
                },
                token: token,
                sessionId: session.id
            };
        } catch (error) {
            console.error('Authentication error:', error);
            throw error;
        }
    }

    async logout(sessionId) {
        try {
            const session = this.activeSessions.get(sessionId);
            if (session) {
                this.activeSessions.delete(sessionId);
                await this.saveSessions();
                await this.logSecurityEvent('logout', { username: session.username });
            }
            
            return { success: true };
        } catch (error) {
            console.error('Logout error:', error);
            throw error;
        }
    }

    async validateSession(sessionId) {
        try {
            const session = this.activeSessions.get(sessionId);
            if (!session) {
                return { valid: false, reason: 'session_not_found' };
            }

            // Check if session has expired
            const config = await this.getSecurityConfig();
            const sessionAge = Date.now() - new Date(session.created).getTime();
            
            if (sessionAge > config.sessionTimeout) {
                this.activeSessions.delete(sessionId);
                await this.saveSessions();
                return { valid: false, reason: 'session_expired' };
            }

            // Update last activity
            session.lastActivity = new Date().toISOString();
            this.activeSessions.set(sessionId, session);

            return {
                valid: true,
                session: session
            };
        } catch (error) {
            console.error('Session validation error:', error);
            return { valid: false, reason: 'validation_error' };
        }
    }

    // User Management
    async getUsers() {
        try {
            const content = await fs.readFile(this.usersPath, 'utf8');
            const data = JSON.parse(content);
            return data.users || [];
        } catch (error) {
            console.error('Error reading users:', error);
            return [];
        }
    }

    async createUser(userData) {
        try {
            const users = await this.getUsers();
            
            // Check if username already exists
            if (users.find(u => u.username === userData.username)) {
                throw new Error('Username already exists');
            }

            // Validate password policy
            await this.validatePassword(userData.password);

            const newUser = {
                id: crypto.randomUUID(),
                username: userData.username,
                email: userData.email,
                passwordHash: await bcrypt.hash(userData.password, 10),
                role: userData.role || 'user',
                permissions: userData.permissions || [],
                created: new Date().toISOString(),
                lastLogin: null,
                active: true
            };

            users.push(newUser);
            await this.saveUsers(users);

            await this.logSecurityEvent('user_created', { username: userData.username, role: newUser.role });

            return {
                success: true,
                user: {
                    id: newUser.id,
                    username: newUser.username,
                    email: newUser.email,
                    role: newUser.role,
                    permissions: newUser.permissions
                }
            };
        } catch (error) {
            console.error('Error creating user:', error);
            throw error;
        }
    }

    async updateUser(userData) {
        try {
            const users = await this.getUsers();
            const userIndex = users.findIndex(u => u.id === userData.id);
            
            if (userIndex === -1) {
                throw new Error('User not found');
            }

            // Update user data
            users[userIndex] = { ...users[userIndex], ...userData };
            await this.saveUsers(users);

            await this.logSecurityEvent('user_updated', { username: userData.username });

            return { success: true };
        } catch (error) {
            console.error('Error updating user:', error);
            throw error;
        }
    }

    async deleteUser(userId) {
        try {
            const users = await this.getUsers();
            const userIndex = users.findIndex(u => u.id === userId);
            
            if (userIndex === -1) {
                throw new Error('User not found');
            }

            const username = users[userIndex].username;
            users.splice(userIndex, 1);
            await this.saveUsers(users);

            // Invalidate all sessions for this user
            for (const [sessionId, session] of this.activeSessions.entries()) {
                if (session.userId === userId) {
                    this.activeSessions.delete(sessionId);
                }
            }
            await this.saveSessions();

            await this.logSecurityEvent('user_deleted', { username });

            return { success: true };
        } catch (error) {
            console.error('Error deleting user:', error);
            throw error;
        }
    }

    async saveUsers(users) {
        const data = {
            users: users,
            lastUpdated: new Date().toISOString()
        };
        await fs.writeFile(this.usersPath, JSON.stringify(data, null, 2));
    }

    // Security Configuration
    async getSecurityConfig() {
        try {
            const content = await fs.readFile(this.securityConfigPath, 'utf8');
            return JSON.parse(content);
        } catch (error) {
            console.error('Error reading security config:', error);
            return {};
        }
    }

    async updateSecurityConfig(config) {
        try {
            await fs.writeFile(this.securityConfigPath, JSON.stringify(config, null, 2));
            await this.logSecurityEvent('security_config_updated', {});
            return { success: true };
        } catch (error) {
            console.error('Error updating security config:', error);
            throw error;
        }
    }

    // Password Policy
    async validatePassword(password) {
        const config = await this.getSecurityConfig();
        const policy = config.passwordPolicy || {};

        if (password.length < (policy.minLength || 8)) {
            throw new Error(`Password must be at least ${policy.minLength || 8} characters long`);
        }

        if (policy.requireUppercase && !/[A-Z]/.test(password)) {
            throw new Error('Password must contain at least one uppercase letter');
        }

        if (policy.requireLowercase && !/[a-z]/.test(password)) {
            throw new Error('Password must contain at least one lowercase letter');
        }

        if (policy.requireNumbers && !/\d/.test(password)) {
            throw new Error('Password must contain at least one number');
        }

        if (policy.requireSpecialChars && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
            throw new Error('Password must contain at least one special character');
        }

        return true;
    }

    // Failed Login Tracking
    async recordFailedLogin(username) {
        try {
            const failedLoginsPath = path.join(process.cwd(), 'data', 'failed-logins.json');
            let failedLogins = {};

            try {
                const content = await fs.readFile(failedLoginsPath, 'utf8');
                failedLogins = JSON.parse(content);
            } catch (error) {
                // File doesn't exist, start with empty object
            }

            if (!failedLogins[username]) {
                failedLogins[username] = {
                    attempts: 0,
                    lastAttempt: null,
                    lockedUntil: null
                };
            }

            failedLogins[username].attempts++;
            failedLogins[username].lastAttempt = new Date().toISOString();

            const config = await this.getSecurityConfig();
            if (failedLogins[username].attempts >= config.maxLoginAttempts) {
                failedLogins[username].lockedUntil = new Date(Date.now() + config.lockoutDuration).toISOString();
            }

            await fs.writeFile(failedLoginsPath, JSON.stringify(failedLogins, null, 2));
        } catch (error) {
            console.error('Error recording failed login:', error);
        }
    }

    async clearFailedLogins(username) {
        try {
            const failedLoginsPath = path.join(process.cwd(), 'data', 'failed-logins.json');
            let failedLogins = {};

            try {
                const content = await fs.readFile(failedLoginsPath, 'utf8');
                failedLogins = JSON.parse(content);
            } catch (error) {
                return; // File doesn't exist, nothing to clear
            }

            if (failedLogins[username]) {
                delete failedLogins[username];
                await fs.writeFile(failedLoginsPath, JSON.stringify(failedLogins, null, 2));
            }
        } catch (error) {
            console.error('Error clearing failed logins:', error);
        }
    }

    async isUserLockedOut(username) {
        try {
            const failedLoginsPath = path.join(process.cwd(), 'data', 'failed-logins.json');
            let failedLogins = {};

            try {
                const content = await fs.readFile(failedLoginsPath, 'utf8');
                failedLogins = JSON.parse(content);
            } catch (error) {
                return false; // File doesn't exist, user is not locked out
            }

            const userFailedLogins = failedLogins[username];
            if (!userFailedLogins || !userFailedLogins.lockedUntil) {
                return false;
            }

            const lockoutExpiry = new Date(userFailedLogins.lockedUntil);
            const now = new Date();

            if (now < lockoutExpiry) {
                return true; // Still locked out
            } else {
                // Lockout has expired, clear it
                await this.clearFailedLogins(username);
                return false;
            }
        } catch (error) {
            console.error('Error checking lockout status:', error);
            return false;
        }
    }

    // Session Management
    async saveSessions() {
        try {
            const sessionsData = {
                sessions: Array.from(this.activeSessions.entries()).map(([id, session]) => ({
                    id,
                    ...session
                })),
                lastUpdated: new Date().toISOString()
            };
            await fs.writeFile(this.sessionsPath, JSON.stringify(sessionsData, null, 2));
        } catch (error) {
            console.error('Error saving sessions:', error);
        }
    }

    async loadSessions() {
        try {
            const content = await fs.readFile(this.sessionsPath, 'utf8');
            const data = JSON.parse(content);

            this.activeSessions.clear();
            if (data.sessions) {
                data.sessions.forEach(session => {
                    this.activeSessions.set(session.id, session);
                });
            }
        } catch (error) {
            console.error('Error loading sessions:', error);
        }
    }

    async getActiveSessions() {
        return Array.from(this.activeSessions.values());
    }

    async invalidateAllSessions(userId = null) {
        try {
            if (userId) {
                // Invalidate sessions for specific user
                for (const [sessionId, session] of this.activeSessions.entries()) {
                    if (session.userId === userId) {
                        this.activeSessions.delete(sessionId);
                    }
                }
            } else {
                // Invalidate all sessions
                this.activeSessions.clear();
            }

            await this.saveSessions();
            return { success: true };
        } catch (error) {
            console.error('Error invalidating sessions:', error);
            throw error;
        }
    }

    // Security Logging
    async logSecurityEvent(event, details) {
        try {
            const config = await this.getSecurityConfig();
            if (!config.auditLogging) return;

            const logPath = path.join(process.cwd(), 'logs', 'security.log');
            await fs.mkdir(path.dirname(logPath), { recursive: true });

            const logEntry = {
                timestamp: new Date().toISOString(),
                event: event,
                details: details,
                source: 'SecurityService'
            };

            const logLine = JSON.stringify(logEntry) + '\n';
            await fs.appendFile(logPath, logLine);
        } catch (error) {
            console.error('Error logging security event:', error);
        }
    }

    async getSecurityLogs(limit = 100) {
        try {
            const logPath = path.join(process.cwd(), 'logs', 'security.log');
            const content = await fs.readFile(logPath, 'utf8');
            const lines = content.trim().split('\n').filter(line => line);

            const logs = lines.slice(-limit).map(line => {
                try {
                    return JSON.parse(line);
                } catch (error) {
                    return null;
                }
            }).filter(log => log !== null);

            return logs.reverse(); // Most recent first
        } catch (error) {
            console.error('Error reading security logs:', error);
            return [];
        }
    }

    // Permission Management
    hasPermission(userPermissions, requiredPermission) {
        if (!userPermissions || !Array.isArray(userPermissions)) {
            return false;
        }

        // Check for wildcard permission
        if (userPermissions.includes('*')) {
            return true;
        }

        // Check for exact permission match
        if (userPermissions.includes(requiredPermission)) {
            return true;
        }

        // Check for wildcard patterns (e.g., 'server.*' matches 'server.start')
        return userPermissions.some(permission => {
            if (permission.endsWith('*')) {
                const prefix = permission.slice(0, -1);
                return requiredPermission.startsWith(prefix);
            }
            return false;
        });
    }

    // Role-based permissions
    getRolePermissions(role) {
        const rolePermissions = {
            'administrator': ['*'],
            'moderator': [
                'server.view',
                'server.restart',
                'players.*',
                'logs.view',
                'plugins.view',
                'plugins.configure'
            ],
            'user': [
                'server.view',
                'logs.view',
                'players.view'
            ]
        };

        return rolePermissions[role] || [];
    }
}

module.exports = SecurityService;
