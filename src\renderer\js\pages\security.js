// Security Management Page
class SecurityPage {
    static async render(container) {
        try {
            console.log('🔐 Rendering Security page...');

            const html = `
                <div class="dashboard-grid">
                    <div class="stat-card">
                        <div class="stat-card-header">
                            <div class="stat-card-title">Total Users</div>
                            <div class="stat-card-icon primary">
                                <i class="fas fa-users"></i>
                            </div>
                        </div>
                        <div class="stat-card-value" id="total-users-count">0</div>
                        <div class="stat-card-change neutral">
                            <i class="fas fa-user"></i>
                            <span>Users</span>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-card-header">
                            <div class="stat-card-title">Active Sessions</div>
                            <div class="stat-card-icon success">
                                <i class="fas fa-user-check"></i>
                            </div>
                        </div>
                        <div class="stat-card-value" id="active-sessions-count">0</div>
                        <div class="stat-card-change neutral">
                            <i class="fas fa-clock"></i>
                            <span>Online</span>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-card-header">
                            <div class="stat-card-title">Security Events</div>
                            <div class="stat-card-icon warning">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                        </div>
                        <div class="stat-card-value" id="security-events-count">0</div>
                        <div class="stat-card-change neutral">
                            <i class="fas fa-shield-alt"></i>
                            <span>Events</span>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-card-header">
                            <div class="stat-card-title">Failed Logins</div>
                            <div class="stat-card-icon error">
                                <i class="fas fa-ban"></i>
                            </div>
                        </div>
                        <div class="stat-card-value" id="failed-logins-count">0</div>
                        <div class="stat-card-change neutral">
                            <i class="fas fa-times"></i>
                            <span>Failed</span>
                        </div>
                    </div>
                </div>

                <div class="dashboard-row">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">User Management</h3>
                            <p class="card-subtitle">Manage user accounts and permissions</p>
                            <div class="card-actions">
                                <button class="btn btn-sm btn-primary" onclick="SecurityPage.showCreateUserModal()">
                                    <i class="fas fa-plus"></i>
                                    Add User
                                </button>
                                <button class="btn btn-sm btn-secondary" onclick="SecurityPage.refreshUsers()">
                                    <i class="fas fa-sync"></i>
                                    Refresh
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="users-table"></div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Active Sessions</h3>
                            <p class="card-subtitle">Monitor active user sessions</p>
                            <div class="card-actions">
                                <button class="btn btn-sm btn-warning" onclick="SecurityPage.invalidateAllSessions()">
                                    <i class="fas fa-sign-out-alt"></i>
                                    Logout All
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="sessions-table"></div>
                        </div>
                    </div>
                </div>

                <div class="dashboard-row">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Security Configuration</h3>
                            <p class="card-subtitle">Configure security policies and settings</p>
                            <div class="card-actions">
                                <button class="btn btn-sm btn-primary" onclick="SecurityPage.saveSecurityConfig()">
                                    <i class="fas fa-save"></i>
                                    Save Settings
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="security-config-form" id="security-config-form">
                                <!-- Security configuration form will be loaded here -->
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Security Logs</h3>
                            <p class="card-subtitle">View security events and audit logs</p>
                            <div class="card-actions">
                                <button class="btn btn-sm btn-secondary" onclick="SecurityPage.refreshSecurityLogs()">
                                    <i class="fas fa-sync"></i>
                                    Refresh
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="security-logs-table"></div>
                        </div>
                    </div>
                </div>

                <!-- Create/Edit User Modal -->
                <div class="modal-overlay" id="user-modal">
                    <div class="modal">
                        <div class="modal-header">
                            <h3 class="modal-title" id="user-modal-title">Create User</h3>
                            <button class="modal-close" onclick="SecurityPage.closeUserModal()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="modal-body">
                            <form id="user-form">
                                <div class="form-group">
                                    <label class="form-label">Username</label>
                                    <input type="text" class="form-control" id="user-username" required>
                                    <div class="form-text">Username must be unique</div>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">Email</label>
                                    <input type="email" class="form-control" id="user-email" required>
                                </div>

                                <div class="form-group" id="password-group">
                                    <label class="form-label">Password</label>
                                    <input type="password" class="form-control" id="user-password" required>
                                    <div class="form-text">Password must meet security requirements</div>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">Role</label>
                                    <select class="form-control" id="user-role" required>
                                        <option value="">Select Role</option>
                                        <option value="administrator">Administrator</option>
                                        <option value="moderator">Moderator</option>
                                        <option value="user">User</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">Status</label>
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="user-active" checked>
                                        <label class="form-check-label" for="user-active">Active</label>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button class="btn btn-secondary" onclick="SecurityPage.closeUserModal()">
                                Cancel
                            </button>
                            <button class="btn btn-primary" onclick="SecurityPage.saveUser()">
                                <i class="fas fa-save"></i>
                                Save User
                            </button>
                        </div>
                    </div>
                </div>
            `;

            container.innerHTML = html;
            console.log('✅ Security page HTML rendered');

            // Initialize the page with timeout protection
            console.log('🔄 Initializing Security page...');
            await this.initializePage();
            console.log('✅ Security page initialized successfully');

        } catch (error) {
            console.error('❌ Error rendering Security page:', error);

            // Show error state instead of infinite loading
            container.innerHTML = `
                <div class="alert alert-error">
                    <h3>⚠️ Failed to Load Security Page</h3>
                    <p>An error occurred while loading the security management page:</p>
                    <p><strong>${error.message}</strong></p>
                    <div class="alert-actions">
                        <button class="btn btn-primary" onclick="window.app.loadPage('security')">
                            <i class="fas fa-redo"></i> Retry
                        </button>
                        <button class="btn btn-secondary" onclick="window.app.loadPage('dashboard')">
                            <i class="fas fa-home"></i> Go to Dashboard
                        </button>
                    </div>
                </div>
            `;

            // Still throw the error so the app can handle it
            throw error;
        }
    }

    static async initializePage() {
        try {
            console.log('🔄 Initializing Security page data...');

            // Initialize data structures
            this.users = [];
            this.sessions = [];
            this.securityLogs = [];
            this.securityConfig = {};
            this.editingUser = null;

            // Load initial data with individual error handling
            console.log('📊 Loading users...');
            await this.loadUsers();

            console.log('🔐 Loading active sessions...');
            await this.loadActiveSessions();

            console.log('⚙️ Loading security config...');
            await this.loadSecurityConfig();

            console.log('📋 Loading security logs...');
            await this.loadSecurityLogs();

            console.log('📈 Updating stats...');
            await this.updateStats();

            console.log('✅ Security page initialization completed');

        } catch (error) {
            console.error('❌ Error during Security page initialization:', error);

            // Show error notification but don't prevent page from loading
            if (typeof toastNotifications !== 'undefined') {
                toastNotifications.show('Warning', 'Some security data could not be loaded. Check console for details.', 'warning');
            }

            // Initialize with empty data to prevent further errors
            this.users = this.users || [];
            this.sessions = this.sessions || [];
            this.securityLogs = this.securityLogs || [];
            this.securityConfig = this.securityConfig || {};

            // Update stats with available data
            try {
                await this.updateStats();
            } catch (statsError) {
                console.error('Error updating stats:', statsError);
            }
        }
    }

    static async loadUsers() {
        try {
            if (window.electronAPI) {
                this.users = await window.electronAPI.getUsers();
                this.renderUsersTable();
                console.log(`✅ Loaded ${this.users.length} users`);
            } else {
                console.warn('⚠️ electronAPI not available, using mock data');
                this.users = []; // Use empty array as fallback
                this.renderUsersTable();
            }
        } catch (error) {
            console.error('❌ Error loading users:', error);
            this.users = []; // Ensure users is always an array
            this.renderUsersTable();

            if (typeof toastNotifications !== 'undefined') {
                toastNotifications.show('Error', 'Failed to load users: ' + error.message, 'error');
            }
        }
    }

    static renderUsersTable() {
        const container = document.getElementById('users-table');
        if (!container) return;

        if (this.users.length === 0) {
            container.innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    No users found
                </div>
            `;
            return;
        }

        const table = new DataTable(container, {
            columns: [
                { key: 'username', title: 'Username', sortable: true },
                { key: 'email', title: 'Email', sortable: true },
                {
                    key: 'role',
                    title: 'Role',
                    sortable: true,
                    render: (value) => `<span class="badge badge-${this.getRoleBadgeClass(value)}">${value}</span>`
                },
                {
                    key: 'lastLogin',
                    title: 'Last Login',
                    sortable: true,
                    render: (value) => value ? new Date(value).toLocaleString() : 'Never'
                },
                {
                    key: 'active',
                    title: 'Status',
                    sortable: true,
                    render: (value) => `<span class="badge badge-${value ? 'success' : 'error'}">${value ? 'Active' : 'Inactive'}</span>`
                },
                {
                    key: 'actions',
                    title: 'Actions',
                    render: (value, row) => `
                        <div class="btn-group">
                            <button class="btn btn-sm btn-primary" onclick="SecurityPage.editUser('${row.id}')" title="Edit">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-warning" onclick="SecurityPage.invalidateUserSessions('${row.id}')" title="Logout User">
                                <i class="fas fa-sign-out-alt"></i>
                            </button>
                            <button class="btn btn-sm btn-error" onclick="SecurityPage.deleteUser('${row.id}')" title="Delete" ${row.username === 'admin' ? 'disabled' : ''}>
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    `
                }
            ],
            data: this.users,
            pagination: true,
            pageSize: 10
        });
    }

    static getRoleBadgeClass(role) {
        const classes = {
            'administrator': 'error',
            'moderator': 'warning',
            'user': 'secondary'
        };
        return classes[role] || 'secondary';
    }

    static async loadActiveSessions() {
        try {
            if (window.electronAPI) {
                this.sessions = await window.electronAPI.getActiveSessions();
                this.renderSessionsTable();
                console.log(`✅ Loaded ${this.sessions.length} active sessions`);
            } else {
                console.warn('⚠️ electronAPI not available, using mock data');
                this.sessions = []; // Use empty array as fallback
                this.renderSessionsTable();
            }
        } catch (error) {
            console.error('❌ Error loading active sessions:', error);
            this.sessions = []; // Ensure sessions is always an array
            this.renderSessionsTable();

            if (typeof toastNotifications !== 'undefined') {
                toastNotifications.show('Error', 'Failed to load active sessions: ' + error.message, 'error');
            }
        }
    }

    static renderSessionsTable() {
        const container = document.getElementById('sessions-table');
        if (!container) return;

        if (this.sessions.length === 0) {
            container.innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    No active sessions
                </div>
            `;
            return;
        }

        const table = new DataTable(container, {
            columns: [
                { key: 'username', title: 'Username', sortable: true },
                {
                    key: 'created',
                    title: 'Login Time',
                    sortable: true,
                    render: (value) => new Date(value).toLocaleString()
                },
                {
                    key: 'lastActivity',
                    title: 'Last Activity',
                    sortable: true,
                    render: (value) => new Date(value).toLocaleString()
                },
                { key: 'ipAddress', title: 'IP Address', sortable: true, render: (value) => value || 'Unknown' },
                {
                    key: 'actions',
                    title: 'Actions',
                    render: (value, row) => `
                        <button class="btn btn-sm btn-warning" onclick="SecurityPage.invalidateSession('${row.id}')" title="Logout">
                            <i class="fas fa-sign-out-alt"></i>
                            Logout
                        </button>
                    `
                }
            ],
            data: this.sessions,
            pagination: true,
            pageSize: 10
        });
    }

    static async loadSecurityConfig() {
        try {
            if (window.electronAPI) {
                this.securityConfig = await window.electronAPI.getSecurityConfig();
                this.renderSecurityConfigForm();
                console.log('✅ Loaded security configuration');
            } else {
                console.warn('⚠️ electronAPI not available, using default config');
                this.securityConfig = {
                    sessionTimeout: ********, // 24 hours
                    maxLoginAttempts: 5,
                    lockoutDuration: 900000, // 15 minutes
                    auditLogging: true,
                    passwordPolicy: {
                        minLength: 8,
                        requireUppercase: true,
                        requireLowercase: true,
                        requireNumbers: true,
                        requireSpecialChars: false
                    }
                };
                this.renderSecurityConfigForm();
            }
        } catch (error) {
            console.error('❌ Error loading security config:', error);
            this.securityConfig = {}; // Ensure config is always an object
            this.renderSecurityConfigForm();

            if (typeof toastNotifications !== 'undefined') {
                toastNotifications.show('Error', 'Failed to load security configuration: ' + error.message, 'error');
            }
        }
    }

    static renderSecurityConfigForm() {
        const container = document.getElementById('security-config-form');
        if (!container) return;

        const config = this.securityConfig;

        container.innerHTML = `
            <div class="form-row">
                <div class="form-group">
                    <label class="form-label">Session Timeout (hours)</label>
                    <input type="number" class="form-control" id="session-timeout"
                           value="${(config.sessionTimeout || ********) / (1000 * 60 * 60)}" min="1" max="168">
                    <div class="form-text">How long sessions remain active</div>
                </div>

                <div class="form-group">
                    <label class="form-label">Max Login Attempts</label>
                    <input type="number" class="form-control" id="max-login-attempts"
                           value="${config.maxLoginAttempts || 5}" min="1" max="20">
                    <div class="form-text">Failed attempts before account lockout</div>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label class="form-label">Lockout Duration (minutes)</label>
                    <input type="number" class="form-control" id="lockout-duration"
                           value="${(config.lockoutDuration || 900000) / (1000 * 60)}" min="1" max="1440">
                    <div class="form-text">How long accounts remain locked</div>
                </div>

                <div class="form-group">
                    <label class="form-label">Audit Logging</label>
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="audit-logging"
                               ${config.auditLogging ? 'checked' : ''}>
                        <label class="form-check-label" for="audit-logging">Enable security event logging</label>
                    </div>
                </div>
            </div>

            <div class="form-section">
                <h4>Password Policy</h4>
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">Minimum Length</label>
                        <input type="number" class="form-control" id="password-min-length"
                               value="${config.passwordPolicy?.minLength || 8}" min="4" max="128">
                    </div>

                    <div class="form-group">
                        <label class="form-label">Requirements</label>
                        <div class="form-checks">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="require-uppercase"
                                       ${config.passwordPolicy?.requireUppercase ? 'checked' : ''}>
                                <label class="form-check-label" for="require-uppercase">Uppercase letters</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="require-lowercase"
                                       ${config.passwordPolicy?.requireLowercase ? 'checked' : ''}>
                                <label class="form-check-label" for="require-lowercase">Lowercase letters</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="require-numbers"
                                       ${config.passwordPolicy?.requireNumbers ? 'checked' : ''}>
                                <label class="form-check-label" for="require-numbers">Numbers</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="require-special-chars"
                                       ${config.passwordPolicy?.requireSpecialChars ? 'checked' : ''}>
                                <label class="form-check-label" for="require-special-chars">Special characters</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    static async loadSecurityLogs() {
        try {
            if (window.electronAPI) {
                this.securityLogs = await window.electronAPI.getSecurityLogs(50);
                this.renderSecurityLogsTable();
                console.log(`✅ Loaded ${this.securityLogs.length} security log entries`);
            } else {
                console.warn('⚠️ electronAPI not available, using mock data');
                this.securityLogs = []; // Use empty array as fallback
                this.renderSecurityLogsTable();
            }
        } catch (error) {
            console.error('❌ Error loading security logs:', error);
            this.securityLogs = []; // Ensure logs is always an array
            this.renderSecurityLogsTable();

            if (typeof toastNotifications !== 'undefined') {
                toastNotifications.show('Error', 'Failed to load security logs: ' + error.message, 'error');
            }
        }
    }

    static renderSecurityLogsTable() {
        const container = document.getElementById('security-logs-table');
        if (!container) return;

        if (this.securityLogs.length === 0) {
            container.innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    No security events found
                </div>
            `;
            return;
        }

        const table = new DataTable(container, {
            columns: [
                {
                    key: 'timestamp',
                    title: 'Time',
                    sortable: true,
                    render: (value) => new Date(value).toLocaleString()
                },
                {
                    key: 'event',
                    title: 'Event',
                    sortable: true,
                    render: (value) => `<span class="badge badge-${this.getEventBadgeClass(value)}">${value}</span>`
                },
                {
                    key: 'details',
                    title: 'Details',
                    render: (value) => {
                        if (value.username) {
                            return `User: ${value.username}${value.reason ? ` (${value.reason})` : ''}`;
                        }
                        return JSON.stringify(value);
                    }
                }
            ],
            data: this.securityLogs,
            pagination: true,
            pageSize: 10
        });
    }

    static getEventBadgeClass(event) {
        const classes = {
            'login_success': 'success',
            'login_failed': 'error',
            'logout': 'secondary',
            'user_created': 'primary',
            'user_updated': 'warning',
            'user_deleted': 'error'
        };
        return classes[event] || 'secondary';
    }

    static async updateStats() {
        try {
            document.getElementById('total-users-count').textContent = this.users.length;
            document.getElementById('active-sessions-count').textContent = this.sessions.length;
            document.getElementById('security-events-count').textContent = this.securityLogs.length;

            // Count failed login events
            const failedLogins = this.securityLogs.filter(log => log.event === 'login_failed').length;
            document.getElementById('failed-logins-count').textContent = failedLogins;
        } catch (error) {
            console.error('Error updating stats:', error);
        }
    }

    static showCreateUserModal() {
        this.editingUser = null;

        const modal = document.getElementById('user-modal');
        const title = document.getElementById('user-modal-title');
        const form = document.getElementById('user-form');

        title.textContent = 'Create User';
        form.reset();
        document.getElementById('password-group').style.display = 'block';

        modal.classList.add('active');
    }

    static editUser(userId) {
        const user = this.users.find(u => u.id === userId);
        if (!user) return;

        this.editingUser = user;

        const modal = document.getElementById('user-modal');
        const title = document.getElementById('user-modal-title');

        title.textContent = 'Edit User';

        // Populate form
        document.getElementById('user-username').value = user.username;
        document.getElementById('user-email').value = user.email;
        document.getElementById('user-role').value = user.role;
        document.getElementById('user-active').checked = user.active;

        // Hide password field for editing
        document.getElementById('password-group').style.display = 'none';

        modal.classList.add('active');
    }

    static closeUserModal() {
        const modal = document.getElementById('user-modal');
        modal.classList.remove('active');
        this.editingUser = null;
    }

    static async saveUser() {
        try {
            const username = document.getElementById('user-username').value;
            const email = document.getElementById('user-email').value;
            const password = document.getElementById('user-password').value;
            const role = document.getElementById('user-role').value;
            const active = document.getElementById('user-active').checked;

            if (!username || !email || !role) {
                toastNotifications.show('Error', 'Please fill in all required fields', 'error');
                return;
            }

            if (!this.editingUser && !password) {
                toastNotifications.show('Error', 'Password is required for new users', 'error');
                return;
            }

            const userData = {
                username,
                email,
                role,
                active
            };

            if (password) {
                userData.password = password;
            }

            if (window.electronAPI) {
                if (this.editingUser) {
                    userData.id = this.editingUser.id;
                    await window.electronAPI.updateUser(userData);
                } else {
                    await window.electronAPI.createUser(userData);
                }
            }

            this.closeUserModal();
            await this.loadUsers();
            await this.updateStats();

        } catch (error) {
            console.error('Error saving user:', error);
            toastNotifications.show('Error', 'Failed to save user: ' + error.message, 'error');
        }
    }

    static deleteUser(userId) {
        const user = this.users.find(u => u.id === userId);
        if (!user) return;

        if (user.username === 'admin') {
            toastNotifications.show('Error', 'Cannot delete the admin user', 'error');
            return;
        }

        showConfirmDialog(
            'Delete User',
            `Are you sure you want to delete user "${user.username}"? This action cannot be undone.`,
            async () => {
                try {
                    if (window.electronAPI) {
                        await window.electronAPI.deleteUser(userId);
                        await this.loadUsers();
                        await this.loadActiveSessions();
                        await this.updateStats();
                    }
                } catch (error) {
                    console.error('Error deleting user:', error);
                    toastNotifications.show('Error', 'Failed to delete user: ' + error.message, 'error');
                }
            }
        );
    }

    static async invalidateSession(sessionId) {
        try {
            if (window.electronAPI) {
                await window.electronAPI.invalidateSessions();
                await this.loadActiveSessions();
                await this.updateStats();
                toastNotifications.show('Success', 'Session terminated successfully', 'success');
            }
        } catch (error) {
            console.error('Error invalidating session:', error);
            toastNotifications.show('Error', 'Failed to terminate session: ' + error.message, 'error');
        }
    }

    static async invalidateUserSessions(userId) {
        try {
            if (window.electronAPI) {
                await window.electronAPI.invalidateSessions(userId);
                await this.loadActiveSessions();
                await this.updateStats();
                toastNotifications.show('Success', 'User sessions terminated successfully', 'success');
            }
        } catch (error) {
            console.error('Error invalidating user sessions:', error);
            toastNotifications.show('Error', 'Failed to terminate user sessions: ' + error.message, 'error');
        }
    }

    static invalidateAllSessions() {
        showConfirmDialog(
            'Logout All Users',
            'Are you sure you want to logout all users? This will terminate all active sessions.',
            async () => {
                try {
                    if (window.electronAPI) {
                        await window.electronAPI.invalidateSessions();
                        await this.loadActiveSessions();
                        await this.updateStats();
                        toastNotifications.show('Success', 'All sessions terminated successfully', 'success');
                    }
                } catch (error) {
                    console.error('Error invalidating all sessions:', error);
                    toastNotifications.show('Error', 'Failed to terminate sessions: ' + error.message, 'error');
                }
            }
        );
    }

    static async saveSecurityConfig() {
        try {
            const config = {
                sessionTimeout: parseInt(document.getElementById('session-timeout').value) * 1000 * 60 * 60,
                maxLoginAttempts: parseInt(document.getElementById('max-login-attempts').value),
                lockoutDuration: parseInt(document.getElementById('lockout-duration').value) * 1000 * 60,
                auditLogging: document.getElementById('audit-logging').checked,
                passwordPolicy: {
                    minLength: parseInt(document.getElementById('password-min-length').value),
                    requireUppercase: document.getElementById('require-uppercase').checked,
                    requireLowercase: document.getElementById('require-lowercase').checked,
                    requireNumbers: document.getElementById('require-numbers').checked,
                    requireSpecialChars: document.getElementById('require-special-chars').checked
                }
            };

            if (window.electronAPI) {
                await window.electronAPI.updateSecurityConfig(config);
                this.securityConfig = config;
            }
        } catch (error) {
            console.error('Error saving security config:', error);
            toastNotifications.show('Error', 'Failed to save security configuration: ' + error.message, 'error');
        }
    }

    static async refreshUsers() {
        await this.loadUsers();
        await this.updateStats();
        toastNotifications.show('Success', 'Users list refreshed', 'success');
    }

    static async refreshSecurityLogs() {
        await this.loadSecurityLogs();
        await this.updateStats();
        toastNotifications.show('Success', 'Security logs refreshed', 'success');
    }
}
